/* Home Container */
.home-container {
  min-height: 100vh;
  background: white;
  padding: 20px;
  color: black;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 20px;
  text-align: center;
}

.hero-content h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

/* Main Content */
.main-content {
  background: white;
  margin: -50px 20px 0;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  overflow: hidden;
}

/* Auth Section */
.auth-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  padding: 60px 40px;
}

.login-section {
  padding: 20px;
}

.login-section h2 {
  color: #333;
  margin-bottom: 30px;
  font-size: 2rem;
  font-weight: 600;
}

/* Form Styles */
.login-form {
  max-width: 400px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.form-control {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.error {
  color: #e74c3c;
  font-size: 14px;
  margin-top: 5px;
}

.error-message {
  background: #fee;
  color: #c33;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
  border: 1px solid #fcc;
}

/* Buttons */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  width: 100%;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.link-btn {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
}

.link-btn:hover {
  color: #764ba2;
}

/* Auth Links */
.auth-links {
  margin-top: 20px;
  text-align: center;
}

.auth-links p {
  color: #666;
}

/* Features Section */
.features-section {
  padding: 20px;
}

.features-section h3 {
  color: #333;
  margin-bottom: 30px;
  font-size: 1.8rem;
  font-weight: 600;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.feature-card {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.feature-card h4 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.feature-card p {
  color: #666;
  line-height: 1.5;
  margin: 0;
}

/* Dashboard Preview */
.dashboard-preview {
  padding: 60px 40px;
}

.welcome-section {
  text-align: center;
  margin-bottom: 40px;
}

.welcome-section h2 {
  color: #333;
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.welcome-section p {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 30px;
}

.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-buttons .btn {
  min-width: 150px;
}

/* User Profile */
.user-profile {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 40px;
  border: 1px solid #e9ecef;
}

.user-profile h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.5rem;
  font-weight: 600;
}

.profile-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.profile-item {
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.profile-item:last-child {
  border-bottom: none;
}

.profile-item strong {
  color: #333;
  font-weight: 600;
}

/* Users Section */
.users-section {
  margin-top: 40px;
}

.users-section h3 {
  color: #333;
  margin-bottom: 25px;
  font-size: 1.5rem;
  font-weight: 600;
}

.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.user-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.user-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.user-info h4 {
  color: #333;
  margin-bottom: 8px;
  font-size: 1.1rem;
  font-weight: 600;
}

.user-info p {
  color: #666;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.user-role {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .auth-section {
    grid-template-columns: 1fr;
    padding: 40px 20px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-preview {
    padding: 40px 20px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .users-grid {
    grid-template-columns: 1fr;
  }

  .main-content {
    margin: -30px 10px 0;
    border-radius: 15px;
  }
}