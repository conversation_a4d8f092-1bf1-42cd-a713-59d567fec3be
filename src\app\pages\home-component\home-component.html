
<div class="home-container">
  <h1>TEST - Trang chủ đang hoạt động!</h1>
  <p>Nếu bạn thấy dòng này thì HomeComponent đã được load thành công.</p>

  <!-- Hero Section -->
  <div class="hero-section">
    <div class="hero-content">
      <h1>Chào mừng đến với JWT Demo App</h1>
      <p class="hero-subtitle">Ứng dụng demo xác thực JWT với Angular và Spring Boot</p>
    </div>
  </div>

  <!-- Main Content -->
  <div class="main-content">
    <!-- Nếu chưa đăng nhập -->
    <div *ngIf="!isAuthenticated" class="auth-section">
      <div class="login-section">
        <h2>Đăng nhập</h2>
        <form [formGroup]="loginForm" (ngSubmit)="onLogin()" class="login-form">
          <div class="form-group">
            <label for="username">Tên đăng nhập:</label>
            <input
              type="text"
              id="username"
              formControlName="username"
              class="form-control"
              placeholder="Nhập tên đăng nhập">
            <div *ngIf="loginForm.get('username')?.invalid && loginForm.get('username')?.touched" class="error">
              Tên đăng nhập là bắt buộc
            </div>
          </div>

          <div class="form-group">
            <label for="password">Mật khẩu:</label>
            <input
              type="password"
              id="password"
              formControlName="password"
              class="form-control"
              placeholder="Nhập mật khẩu">
            <div *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched" class="error">
              Mật khẩu là bắt buộc
            </div>
          </div>

          <div *ngIf="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>

          <button
            type="submit"
            [disabled]="loginForm.invalid || loading"
            class="btn btn-primary">
            {{ loading ? 'Đang đăng nhập...' : 'Đăng nhập' }}
          </button>
        </form>

        <div class="auth-links">
          <p>Chưa có tài khoản? <button (click)="goToRegister()" class="link-btn">Đăng ký ngay</button></p>
        </div>
      </div>

      <!-- Features Section -->
      <div class="features-section">
        <h3>Tính năng của ứng dụng</h3>
        <div class="features-grid">
          <div class="feature-card">
            <h4>🔐 Xác thực JWT</h4>
            <p>Hệ thống xác thực an toàn với JSON Web Token</p>
          </div>
          <div class="feature-card">
            <h4>👤 Quản lý người dùng</h4>
            <p>Đăng ký, đăng nhập và quản lý thông tin cá nhân</p>
          </div>
          <div class="feature-card">
            <h4>🛡️ Bảo mật</h4>
            <p>Interceptor tự động thêm token vào các request</p>
          </div>
          <div class="feature-card">
            <h4>📊 Dashboard</h4>
            <p>Giao diện quản lý dành cho người dùng đã đăng nhập</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Nếu đã đăng nhập -->
    <div *ngIf="isAuthenticated" class="dashboard-preview">
      <div class="welcome-section">
        <h2>Xin chào, {{ currentUser?.username }}!</h2>
        <p>Bạn đã đăng nhập thành công vào hệ thống.</p>

        <div class="action-buttons">
          <button (click)="goToDashboard()" class="btn btn-primary">
            Vào Dashboard
          </button>
          <button (click)="logout()" class="btn btn-secondary">
            Đăng xuất
          </button>
        </div>
      </div>

      <!-- User Profile Section -->
      <div *ngIf="currentUser" class="user-profile">
        <h3>Thông tin cá nhân</h3>
        <div class="profile-card">
          <div class="profile-item">
            <strong>ID:</strong> {{ currentUser.id }}
          </div>
          <div class="profile-item">
            <strong>Tên đăng nhập:</strong> {{ currentUser.username }}
          </div>
          <div class="profile-item">
            <strong>Email:</strong> {{ currentUser.email }}
          </div>
          <div class="profile-item">
            <strong>Vai trò:</strong> {{ currentUser.role }}
          </div>
        </div>
      </div>

      <!-- Users List Section -->
      <div *ngIf="userList.length > 0" class="users-section">
        <h3>Danh sách người dùng ({{ userList.length }})</h3>
        <div class="users-grid">
          <div *ngFor="let user of userList" class="user-card">
            <div class="user-info">
              <h4>{{ user.username }}</h4>
              <p>{{ user.email }}</p>
              <span class="user-role">{{ user.role }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>