import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  template: `
    <div class="login-container">
      <h2>Đăng nhập</h2>
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
        <div class="form-group">
          <label>Username:</label>
          <input type="text" formControlName="username" class="form-control">
          <div *ngIf="loginForm.get('username')?.invalid && loginForm.get('username')?.touched" class="error">
            Username là bắt buộc
          </div>
        </div>

        <div class="form-group">
          <label>Password:</label>
          <input type="password" formControlName="password" class="form-control">
          <div *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched" class="error">
            Password là bắt buộc
          </div>
        </div>

        <button type="submit" [disabled]="loginForm.invalid || loading" class="btn-primary">
          {{ loading ? 'Đang đăng nhập...' : 'Đăng nhập' }}
        </button>

        <div *ngIf="errorMessage" class="error">{{ errorMessage }}</div>
      </form>
    </div>
  `,
  styles: [`
    .login-container { max-width: 400px; margin: 50px auto; padding: 20px; }
    .form-group { margin-bottom: 15px; }
    .form-control { width: 100%; padding: 8px; margin-top: 5px; }
    .btn-primary { width: 100%; padding: 10px; background: #007bff; color: white; border: none; }
    .error { color: red; font-size: 12px; margin-top: 5px; }
  `]
})
export class LoginComponent {
  loginForm: FormGroup;
  loading = false;
  errorMessage = '';

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router
  ) {
    this.loginForm = this.fb.group({
      username: ['', Validators.required],
      password: ['', Validators.required]
    });
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      this.loading = true;
      this.errorMessage = '';

      this.authService.login(this.loginForm.value).subscribe({
        next: (response) => {
          console.log('Đăng nhập thành công:', response);
          this.router.navigate(['/dashboard']);
        },
        error: (error) => {
          this.errorMessage = 'Đăng nhập thất bại. Vui lòng kiểm tra lại thông tin.';
          this.loading = false;
        },
        complete: () => {
          this.loading = false;
        }
      });
    }
  }
}