import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { AuthService, User } from '../../services/auth.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="dashboard-container">
      <h2>Dashboard</h2>
      
      <div *ngIf="user" class="user-info">
        <h3>Thông tin người dùng:</h3>
        <p><strong>ID:</strong> {{ user.id }}</p>
        <p><strong>Username:</strong> {{ user.username }}</p>
        <p><strong>Email:</strong> {{ user.email }}</p>
        <p><strong>Role:</strong> {{ user.role }}</p>
      </div>

      <button (click)="logout()" class="btn-danger"><PERSON><PERSON>ng xuất</button>
    </div>
  `,
  styles: [`
    .dashboard-container { max-width: 600px; margin: 50px auto; padding: 20px; }
    .user-info { background: #f8f9fa; padding: 15px; margin: 20px 0; border-radius: 5px; }
    .btn-danger { padding: 10px 20px; background: #dc3545; color: white; border: none; }
  `]
})
export class DashboardComponent implements OnInit {
  user: User | null = null;

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadProfile();
  }

  loadProfile(): void {
    this.authService.getProfile().subscribe({
      next: (user) => {
        this.user = user;
      },
      error: (error) => {
        console.error('Lỗi khi tải profile:', error);
        this.authService.logout();
        this.router.navigate(['/login']);
      }
    });
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/login']);
  }
}