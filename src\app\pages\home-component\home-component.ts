import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { AuthService, User } from '../../services/auth.service';

@Component({
  selector: 'app-home-component',
  standalone: true,
  imports: [CommonModule, RouterLink, ReactiveFormsModule],
  templateUrl: './home-component.html',
  styleUrl: './home-component.css'
})
export class HomeComponent implements OnInit {
  loginForm: FormGroup;
  loading = false;
  errorMessage = '';
  isAuthenticated = false;
  currentUser: User | null = null;
  userList: User[] = [];

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router
  ) {
    this.loginForm = this.fb.group({
      username: ['', Validators.required],
      password: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    // Kiểm tra trạng thái đăng nhập
    this.authService.isAuthenticated$.subscribe(isAuth => {
      this.isAuthenticated = isAuth;
      if (isAuth) {
        this.loadUserProfile();
        this.loadUserList();
      }
    });
  }

  onLogin(): void {
    if (this.loginForm.valid) {
      this.loading = true;
      this.errorMessage = '';

      this.authService.login(this.loginForm.value).subscribe({
        next: (response) => {
          console.log('Đăng nhập thành công:', response);
          this.loginForm.reset();
        },
        error: (error) => {
          console.error('Lỗi đăng nhập:', error);
          this.errorMessage = 'Đăng nhập thất bại. Vui lòng kiểm tra lại thông tin.';
          this.loading = false;
        },
        complete: () => {
          this.loading = false;
        }
      });
    }
  }

  loadUserProfile(): void {
    this.authService.getProfile().subscribe({
      next: (user) => {
        this.currentUser = user;
      },
      error: (error) => {
        console.error('Lỗi khi tải profile:', error);
      }
    });
  }

  loadUserList(): void {
    this.authService.getUserList().subscribe({
      next: (users) => {
        this.userList = users;
      },
      error: (error) => {
        console.error('Lỗi khi tải danh sách users:', error);
      }
    });
  }

  logout(): void {
    this.authService.logout();
    this.currentUser = null;
    this.userList = [];
    this.loginForm.reset();
  }

  goToDashboard(): void {
    this.router.navigate(['/dashboard']);
  }

  goToRegister(): void {
    this.router.navigate(['/register']);
  }
}
