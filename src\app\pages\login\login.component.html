<div class="container">
  <div class="login-form">
    <h2>JWT Authentication Demo</h2>
    
    <form (ngSubmit)="onSubmit()" #loginForm="ngForm">
      <div class="form-group">
        <label for="username">Username:</label>
        <input 
          type="text" 
          id="username" 
          name="username"
          [(ngModel)]="credentials.username" 
          required
          class="form-control">
      </div>

      <div class="form-group">
        <label for="password">Password:</label>
        <input 
          type="password" 
          id="password" 
          name="password"
          [(ngModel)]="credentials.password" 
          required
          class="form-control">
      </div>

      <div class="error-message" *ngIf="errorMessage">
        {{ errorMessage }}
      </div>

      <button 
        type="submit" 
        [disabled]="!loginForm.form.valid || isLoading"
        class="btn btn-primary">
        {{ isLoading ? 'Logging in...' : 'Login' }}
      </button>
    </form>
  </div>
</div>