import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../services/auth.service';

@Component({
    selector: 'app-register',
    standalone: true,
    imports: [CommonModule, ReactiveFormsModule],
    template: `
    <div class="register-container">
      <h2>Đăng ký</h2>
      <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
        <div class="form-group">
          <label>Username:</label>
          <input type="text" formControlName="username" class="form-control">
        </div>

        <div class="form-group">
          <label>Email:</label>
          <input type="email" formControlName="email" class="form-control">
        </div>

        <div class="form-group">
          <label>Password:</label>
          <input type="password" formControlName="password" class="form-control">
        </div>

        <button type="submit" [disabled]="registerForm.invalid || loading" class="btn-primary">
          {{ loading ? 'Đang đăng ký...' : 'Đăng ký' }}
        </button>

        <div *ngIf="errorMessage" class="error">{{ errorMessage }}</div>
        <div *ngIf="successMessage" class="success">{{ successMessage }}</div>
      </form>
    </div>
  `,
    styles: [`
    .register-container { max-width: 400px; margin: 50px auto; padding: 20px; }
    .form-group { margin-bottom: 15px; }
    .form-control { width: 100%; padding: 8px; margin-top: 5px; }
    .btn-primary { width: 100%; padding: 10px; background: #007bff; color: white; border: none; }
    .error { color: red; font-size: 12px; margin-top: 5px; }
    .success { color: green; font-size: 12px; margin-top: 5px; }
  `]
})
export class RegisterComponent {
    registerForm: FormGroup;
    loading = false;
    errorMessage = '';
    successMessage = '';

    constructor(
        private fb: FormBuilder,
        private authService: AuthService,
        private router: Router
    ) {
        this.registerForm = this.fb.group({
            username: ['', Validators.required],
            email: ['', [Validators.required, Validators.email]],
            password: ['', [Validators.required, Validators.minLength(6)]]
        });
    }

    onSubmit(): void {
        if (this.registerForm.valid) {
            this.loading = true;
            this.errorMessage = '';
            this.successMessage = '';

            this.authService.register(this.registerForm.value).subscribe({
                next: (response) => {
                    this.successMessage = 'Đăng ký thành công! Vui lòng đăng nhập.';
                    setTimeout(() => {
                        this.router.navigate(['/login']);
                    }, 2000);
                },
                error: (error) => {
                    this.errorMessage = 'Đăng ký thất bại. Username có thể đã tồn tại.';
                    this.loading = false;
                },
                complete: () => {
                    this.loading = false;
                }
            });
        }
    }
}