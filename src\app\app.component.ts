import { Component } from '@angular/core';
import { RouterOutlet, RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { AuthService } from './services/auth.service';

@Component({
    selector: 'app-root',
    standalone: true,
    imports: [RouterOutlet, RouterLink, CommonModule],
    template: `
    <nav class="navbar">
      <div class="nav-brand">JWT Demo App</div>
      <div class="nav-links">
        <a *ngIf="!(authService.isAuthenticated$ | async)" routerLink="/login">Đăng nhập</a>
        <a *ngIf="!(authService.isAuthenticated$ | async)" routerLink="/register">Đ<PERSON>ng ký</a>
        <a *ngIf="authService.isAuthenticated$ | async" routerLink="/dashboard">Dashboard</a>
      </div>
    </nav>
    
    <main>
      <router-outlet></router-outlet>
    </main>
  `,
    styles: [`
    .navbar { background: #343a40; color: white; padding: 1rem; display: flex; justify-content: space-between; }
    .nav-brand { font-weight: bold; font-size: 1.2rem; }
    .nav-links a { color: white; text-decoration: none; margin-left: 1rem; }
    .nav-links a:hover { text-decoration: underline; }
    main { min-height: calc(100vh - 60px); }
  `]
})
export class AppComponent {
    constructor(public authService: AuthService) { }
}